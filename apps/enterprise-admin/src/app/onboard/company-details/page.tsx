'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  ArrowLeft,
  Building,
  Mail,
  Globe,
  CheckCircle,
  User,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { OnboardTenantDto } from 'apps/enterprise-admin/src/redux/types/onboardTenant';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { useOnboardTenantMutation } from 'apps/enterprise-admin/src/redux/api/tenantOnboardApi';

// 🔹 Form data now matches DTO fields
interface FormData {
  firstName: string;
  lastName: string;
  domain: string;
  contactEmail: string;
  tenantName: string;
}

export default function CompanyDetailsPage() {
  const router = useRouter();
  const [email, setEmail] = useState<string | null>(null);

  // 🚀 RTK Query hook
  const [onboardTenant, { isLoading, isSuccess, isError }] =
    useOnboardTenantMutation();

  // ✅ load email from localStorage
  useEffect(() => {
    const storedEmail = localStorage.getItem('verifiedEmail');
    if (storedEmail) {
      setEmail(storedEmail);
    } else {
      router.push('/onboard');
    }
  }, [router]);

  // ✅ handle reload case
  useEffect(() => {
    const isPageReload =
      performance.navigation.type === performance.navigation.TYPE_RELOAD;
    if (isPageReload) {
      localStorage.removeItem('verifiedEmail');
      router.push('/onboard');
      return;
    }
    const storedEmail = localStorage.getItem('verifiedEmail');
    if (storedEmail) {
      setEmail(storedEmail);
    } else {
      router.push('/onboard');
    }
  }, [router]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormData>({
    defaultValues: {
      firstName: '',
      lastName: '',
      domain: '',
      contactEmail: '',
      tenantName: '',
    },
  });

  // ✅ sync contactEmail once we know it
  useEffect(() => {
    if (email) {
      reset((prev) => ({ ...prev, contactEmail: email }));
    }
  }, [email, reset]);

  const onSubmit = async (data: FormData) => {
    const payload: OnboardTenantDto = {
      firstName: data.firstName,
      lastName: data.lastName,
      adminEmail: data.contactEmail,
      idempotencyKey: crypto.randomUUID(),
      customDomain: data.domain,
      tenantName: data.tenantName,
    };

    await onboardTenant(payload).unwrap();
    router.push('/onboard');
  };

  if (!email) return null;

  if (isSuccess) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          <Card className="rounded-2xl shadow-lg p-8 text-center border-2 border-[#041C91]">
            <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-2xl font-bold text-[#041C91] mb-4">
              Company Setup Complete!
            </h1>
            <p className="text-gray-500 mb-8">
              Your company details have been saved. Continue to subscription.
            </p>
            <Button
              className="w-full h-12 bg-[#041C91] hover:bg-[#01033d] text-white rounded-lg"
              onClick={() => router.push('/onboard/subscription')}
            >
              Continue to Subscription
            </Button>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white flex items-center justify-center p-8">
      <div className="w-full max-w-2xl">
        <Card className="rounded-2xl shadow-lg relative">
          <CardContent className="p-8">
            {/* Header */}
            <div className="text-center mb-8 relative">
              <Button
                variant="ghost"
                className="absolute top-0 left-0 p-2 text-[#041C91] hover:bg-white"
                onClick={() => window.history.back()}
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div className="w-16 h-16 bg-[#041C91] rounded-full flex items-center justify-center mx-auto mb-6">
                <Building className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-2xl font-bold text-[#041C91] mb-2">
                Company Details
              </h1>
              <p className="text-gray-500">
                Provide your company info to complete setup
              </p>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Company Name */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-[#041C91] flex items-center gap-2">
                    <User className="w-4 h-4" /> First Name *
                  </label>
                  <Input
                    {...register('firstName', {
                      required: 'First name is required',
                      minLength: { value: 3, message: 'At least 3 characters' },
                    })}
                    className={`h-12 rounded-lg  border-2 ${
                      errors.firstName ? 'border-red-400' : 'border-[#041C91]'
                    }`}
                    placeholder="Enter first name"
                  />
                  {errors.firstName && (
                    <p className="text-red-400 text-sm">
                      {errors.firstName.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-[#041C91] flex items-center gap-2">
                    <User className="w-4 h-4" /> Last Name *
                  </label>
                  <Input
                    {...register('lastName', {
                      required: 'Last name is required',
                      minLength: { value: 1, message: 'At least 1 character' },
                    })}
                    className={`h-12 rounded-lg border-2 ${
                      errors.lastName ? 'border-red-400' : 'border-[#041C91]'
                    }`}
                    placeholder="Enter last name"
                  />
                  {errors.lastName && (
                    <p className="text-red-400 text-sm">
                      {errors.lastName.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Tenant Name */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-[#041C91] flex items-center gap-2">
                  <Building className="w-4 h-4" /> Company Name *
                </label>
                <Input
                  {...register('tenantName', {
                    required: 'Company name is required',
                    minLength: { value: 3, message: 'At least 3 characters' },
                  })}
                  className={`h-12 rounded-lg border-2 ${
                    errors.tenantName ? 'border-red-400' : 'border-[#041C91]'
                  }`}
                  placeholder="Enter company name"
                />
                {errors.tenantName && (
                  <p className="text-red-400 text-sm">
                    {errors.tenantName.message}
                  </p>
                )}
                <p className="text-xs text-gray-500">
                  This will be the name of your company in the app
                </p>
              </div>

              {/* Contact Email */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-[#041C91] flex items-center gap-2">
                  <Mail className="w-4 h-4" /> Contact Email *
                </label>
                <Input
                  type="email"
                  {...register('contactEmail', {
                    required: 'Email is required',
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                      message: 'Invalid email address',
                    },
                  })}
                  className={`h-12 rounded-lg border-2 ${
                    errors.contactEmail ? 'border-red-400' : 'border-[#041C91]'
                  }`}
                  disabled
                />
                {errors.contactEmail && (
                  <p className="text-red-400 text-sm">
                    {errors.contactEmail.message}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-[#041C91] flex items-center gap-2">
                  <Globe className="w-4 h-4" /> Custom Domain *
                </label>
                <Input
                  {...register('domain', {
                    required: 'Custom domain is required',
                    pattern: {
                      value: /^[a-zA-Z0-9-]+$/,
                      message: 'Only letters, numbers, and hyphens allowed',
                    },
                  })}
                  className={`h-12 rounded-lg border-2 ${
                    errors.domain ? 'border-red-400' : 'border-[#041C91]'
                  }`}
                  placeholder="companyname"
                />
                {errors.domain && (
                  <p className="text-red-400 text-sm">
                    {errors.domain.message}
                  </p>
                )}
                <p className="text-xs text-gray-500">
                  Only enter the domain name (e.g., <b>mycompany</b>). Do not
                  include http://, https://, or .com
                </p>
              </div>

              {/* Submit */}
              <div className="pt-6">
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-12 bg-[#041C91] hover:bg-[#01033d] disabled:bg-gray-400 text-white rounded-lg"
                >
                  {isLoading ? 'Creating...' : 'Create'}
                </Button>
                {isError && (
                  <p className="text-red-500 text-sm mt-2">
                    Something went wrong while creating tenant.
                  </p>
                )}
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
