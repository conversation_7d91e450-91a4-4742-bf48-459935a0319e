// 'use client';

// import { useEffect, useState } from 'react';
// import { useRouter } from 'next/navigation';
// import { useForm } from 'react-hook-form';
// import { Button } from '@/components/ui/button';
// import { Input } from '@/components/ui/input';
// import {
//   ArrowLeft,
//   Building,
//   User,
//   Mail,
//   Globe,
//   CheckCircle,
// } from 'lucide-react';
// import { Card, CardContent } from '@/components/ui/card';

// interface FormData {
//   firstName: string;
//   lastName: string;
//   tenantName?: string;
//   adminEmail: string;
//   customDomain: string;
// }

// export default function CompanyDetailsPage() {
//   const router = useRouter();
//   const [email, setEmail] = useState<string | null>(null);

//   // ✅ Only access window/localStorage inside useEffect
//   useEffect(() => {
//     const storedEmail = localStorage.getItem('verifiedEmail');

//     if (storedEmail) {
//       setEmail(storedEmail);
//     } else {
//       router.push('/onboard');
//     }
//   }, [router]);

//   useEffect(() => {
//     const isPageReload =
//       performance.navigation.type === performance.navigation.TYPE_RELOAD;

//     if (isPageReload) {
//       localStorage.removeItem('verifiedEmail');
//       router.push('/onboard');
//       return;
//     }

//     const storedEmail = localStorage.getItem('verifiedEmail');

//     if (storedEmail) {
//       setEmail(storedEmail);
//     } else {
//       router.push('/onboard');
//     }
//   }, [router]);

//   const {
//     register,
//     handleSubmit,
//     formState: { errors, isSubmitting, isSubmitSuccessful },
//     reset,
//   } = useForm<FormData>({
//     defaultValues: {
//       firstName: '',
//       lastName: '',
//       tenantName: '',
//       adminEmail: '',
//       customDomain: '',
//     },
//   });

//   // ✅ sync adminEmail once we know it
//   useEffect(() => {
//     if (email) {
//       reset((prev) => ({ ...prev, adminEmail: email }));
//     }
//   }, [email, reset]);

//   const onSubmit = async (data: FormData) => {
//     console.log('Form submitted:', data);
//     await new Promise((resolve) => setTimeout(resolve, 1500)); // mock API
//     router.push('/onboard/subscription');
//   };

//   if (!email) return null; // wait for redirect

//   if (isSubmitSuccessful) {
//     return (
//       <div className="min-h-screen bg-white flex items-center justify-center p-8">
//         <div className="w-full max-w-md">
//           <Card className="rounded-2xl shadow-lg p-8 text-center border-2 border-[#041C91]">
//             <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
//               <CheckCircle className="w-8 h-8 text-white" />
//             </div>
//             <h1 className="text-2xl font-bold text-[#041C91] mb-4">
//               Company Setup Complete!
//             </h1>
//             <p className="text-gray-500 mb-8">
//               Your company details have been saved. Continue to subscription.
//             </p>
//             <Button
//               className="w-full h-12 bg-[#041C91] hover:bg-[#01033d] text-white rounded-lg"
//               onClick={() => router.push('/onboard/subscription')}
//             >
//               Continue to Subscription
//             </Button>
//           </Card>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="min-h-screen bg-white flex items-center justify-center p-8">
//       <div className="w-full max-w-2xl">
//         <Card className="rounded-2xl shadow-lg relative">
//           <CardContent className="p-8">
//             {/* Header */}
//             <div className="text-center mb-8 relative">
//               <Button
//                 variant="ghost"
//                 className="absolute top-0 left-0 p-2 text-[#041C91] hover:bg-white"
//                 onClick={() => window.history.back()}
//               >
//                 <ArrowLeft className="w-5 h-5" />
//               </Button>
//               <div className="w-16 h-16 bg-[#041C91] rounded-full flex items-center justify-center mx-auto mb-6">
//                 <Building className="w-8 h-8 text-white" />
//               </div>
//               <h1 className="text-2xl font-bold text-[#041C91] mb-2">
//                 Company Details
//               </h1>
//               <p className="text-gray-500">
//                 Provide your company info to complete setup
//               </p>
//             </div>

//             {/* Form */}
//             <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
//               {/* First & Last Name */}
//               <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//                 <div className="space-y-2">
//                   <label className="text-sm font-medium text-[#041C91] flex items-center gap-2">
//                     <User className="w-4 h-4" /> First Name *
//                   </label>
//                   <Input
//                     {...register('firstName', {
//                       required: 'First name is required',
//                       minLength: { value: 3, message: 'At least 3 characters' },
//                     })}
//                     className={`h-12 rounded-lg  border-2 ${
//                       errors.firstName ? 'border-red-400' : 'border-[#041C91]'
//                     }`}
//                     placeholder="Enter first name"
//                   />
//                   {errors.firstName && (
//                     <p className="text-red-400 text-sm">
//                       {errors.firstName.message}
//                     </p>
//                   )}
//                 </div>

//                 <div className="space-y-2">
//                   <label className="text-sm font-medium text-[#041C91] flex items-center gap-2">
//                     <User className="w-4 h-4" /> Last Name *
//                   </label>
//                   <Input
//                     {...register('lastName', {
//                       required: 'Last name is required',
//                       minLength: { value: 1, message: 'At least 1 character' },
//                     })}
//                     className={`h-12 rounded-lg border-2 ${
//                       errors.lastName ? 'border-red-400' : 'border-[#041C91]'
//                     }`}
//                     placeholder="Enter last name"
//                   />
//                   {errors.lastName && (
//                     <p className="text-red-400 text-sm">
//                       {errors.lastName.message}
//                     </p>
//                   )}
//                 </div>
//               </div>

//               {/* Admin Email */}
//               <div className="space-y-2">
//                 <label className="text-sm font-medium text-[#041C91] flex items-center gap-2">
//                   <Mail className="w-4 h-4" /> Admin Email *
//                 </label>
//                 <Input
//                   type="email"
//                   {...register('adminEmail', {
//                     required: 'Admin email is required',
//                     pattern: {
//                       value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
//                       message: 'Invalid email address',
//                     },
//                   })}
//                   className={`h-12 rounded-lg border-2 ${
//                     errors.adminEmail ? 'border-red-400' : 'border-[#041C91]'
//                   }`}
//                   disabled // prevent user editing after verification
//                 />
//                 {errors.adminEmail && (
//                   <p className="text-red-400 text-sm">
//                     {errors.adminEmail.message}
//                   </p>
//                 )}
//               </div>

//               {/* Tenant Name */}
//               <div className="space-y-2">
//                 <label className="text-sm font-medium text-[#041C91] flex items-center gap-2">
//                   <Building className="w-4 h-4" /> Tenant Name (Optional)
//                 </label>
//                 <Input
//                   {...register('tenantName')}
//                   className="h-12 rounded-lg border-2 border-[#041C91]"
//                   placeholder="my-company-tenant"
//                 />
//               </div>

//               {/* Custom Domain */}
//               <div className="space-y-2">
//                 <label className="text-sm font-medium text-[#041C91] flex items-center gap-2">
//                   <Globe className="w-4 h-4" /> Custom Domain *
//                 </label>
//                 <Input
//                   {...register('customDomain', {
//                     required: 'Custom domain is required',
//                     pattern: {
//                       value: /^[a-zA-Z0-9-]+$/,
//                       message: 'Only letters, numbers, and hyphens allowed',
//                     },
//                   })}
//                   className={`h-12 rounded-lg border-2 ${
//                     errors.customDomain ? 'border-red-400' : 'border-[#041C91]'
//                   }`}
//                   placeholder="companyname"
//                 />
//                 {errors.customDomain && (
//                   <p className="text-red-400 text-sm">
//                     {errors.customDomain.message}
//                   </p>
//                 )}
//                 <p className="text-xs text-gray-500">
//                   Only enter the domain name (e.g., <b>mycompany</b>). Do not
//                   include http://, https://, or .com
//                 </p>
//               </div>

//               {/* Submit */}
//               <div className="pt-6">
//                 <Button
//                   type="submit"
//                   disabled={isSubmitting}
//                   className="w-full h-12 bg-[#041C91] hover:bg-[#01033d] disabled:bg-gray-400 text-white rounded-lg"
//                 >
//                   {isSubmitting ? 'Creating...' : 'Create'}
//                 </Button>
//               </div>
//             </form>
//           </CardContent>
//         </Card>
//       </div>
//     </div>
//   );
// }
'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Building, Mail, Globe, CheckCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { OnboardTenantDto } from 'apps/enterprise-admin/src/redux/types/onboardTenant';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { useOnboardTenantMutation } from 'apps/enterprise-admin/src/redux/api/tenantOnboardApi';

// 🔹 Form data now matches DTO fields
interface FormData {
  name: string;
  domain: string;
  contactEmail: string;
  contactPhone: string;
  address: string;
}

export default function CompanyDetailsPage() {
  const router = useRouter();
  const [email, setEmail] = useState<string | null>(null);

  // 🚀 RTK Query hook
  const [onboardTenant, { isLoading, isSuccess, isError }] =
    useOnboardTenantMutation();

  // ✅ load email from localStorage
  useEffect(() => {
    const storedEmail = localStorage.getItem('verifiedEmail');
    if (storedEmail) {
      setEmail(storedEmail);
    } else {
      router.push('/onboard');
    }
  }, [router]);

  // ✅ handle reload case
  useEffect(() => {
    const isPageReload =
      performance.navigation.type === performance.navigation.TYPE_RELOAD;
    if (isPageReload) {
      localStorage.removeItem('verifiedEmail');
      router.push('/onboard');
      return;
    }
    const storedEmail = localStorage.getItem('verifiedEmail');
    if (storedEmail) {
      setEmail(storedEmail);
    } else {
      router.push('/onboard');
    }
  }, [router]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormData>({
    defaultValues: {
      name: '',
      domain: '',
      contactEmail: '',
      contactPhone: '',
      address: '',
    },
  });

  // ✅ sync contactEmail once we know it
  useEffect(() => {
    if (email) {
      reset((prev) => ({ ...prev, contactEmail: email }));
    }
  }, [email, reset]);

  // ✅ submit handler
  const onSubmit = async (data: FormData) => {
    try {
      const payload: OnboardTenantDto = {
        name: data.name,
        domain: data.domain,
        contactEmail: data.contactEmail,
        contactPhone: data.contactPhone,
        address: data.address,
      };

      await onboardTenant(payload).unwrap();
      router.push('/onboard/subscription');
    } catch (err) {
      console.error('❌ Tenant onboard failed:', err);
    }
  };

  if (!email) return null;

  if (isSuccess) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          <Card className="rounded-2xl shadow-lg p-8 text-center border-2 border-[#041C91]">
            <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-2xl font-bold text-[#041C91] mb-4">
              Company Setup Complete!
            </h1>
            <p className="text-gray-500 mb-8">
              Your company details have been saved. Continue to subscription.
            </p>
            <Button
              className="w-full h-12 bg-[#041C91] hover:bg-[#01033d] text-white rounded-lg"
              onClick={() => router.push('/onboard/subscription')}
            >
              Continue to Subscription
            </Button>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white flex items-center justify-center p-8">
      <div className="w-full max-w-2xl">
        <Card className="rounded-2xl shadow-lg relative">
          <CardContent className="p-8">
            {/* Header */}
            <div className="text-center mb-8 relative">
              <Button
                variant="ghost"
                className="absolute top-0 left-0 p-2 text-[#041C91] hover:bg-white"
                onClick={() => window.history.back()}
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div className="w-16 h-16 bg-[#041C91] rounded-full flex items-center justify-center mx-auto mb-6">
                <Building className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-2xl font-bold text-[#041C91] mb-2">
                Company Details
              </h1>
              <p className="text-gray-500">
                Provide your company info to complete setup
              </p>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Company Name */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-[#041C91] flex items-center gap-2">
                  Company Name *
                </label>
                <Input
                  {...register('name', {
                    required: 'Company name is required',
                  })}
                  className={`h-12 rounded-lg border-2 ${
                    errors.name ? 'border-red-400' : 'border-[#041C91]'
                  }`}
                  placeholder="Enter company name"
                />
                {errors.name && (
                  <p className="text-red-400 text-sm">{errors.name.message}</p>
                )}
              </div>

              {/* Contact Email */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-[#041C91] flex items-center gap-2">
                  <Mail className="w-4 h-4" /> Contact Email *
                </label>
                <Input
                  type="email"
                  {...register('contactEmail', {
                    required: 'Email is required',
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                      message: 'Invalid email address',
                    },
                  })}
                  className={`h-12 rounded-lg border-2 ${
                    errors.contactEmail ? 'border-red-400' : 'border-[#041C91]'
                  }`}
                  disabled
                />
                {errors.contactEmail && (
                  <p className="text-red-400 text-sm">
                    {errors.contactEmail.message}
                  </p>
                )}
              </div>

              {/* Domain */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-[#041C91] flex items-center gap-2">
                  <Globe className="w-4 h-4" /> Domain *
                </label>
                <Input
                  {...register('domain', {
                    required: 'Domain is required',
                    pattern: {
                      value: /^[a-zA-Z0-9-]+$/,
                      message: 'Only letters, numbers, and hyphens allowed',
                    },
                  })}
                  className={`h-12 rounded-lg border-2 ${
                    errors.domain ? 'border-red-400' : 'border-[#041C91]'
                  }`}
                  placeholder="companyname"
                />
                {errors.domain && (
                  <p className="text-red-400 text-sm">
                    {errors.domain.message}
                  </p>
                )}
              </div>

              {/* Contact Phone */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-[#041C91]">
                  Contact Phone
                </label>
                <Input
                  {...register('contactPhone')}
                  className="h-12 rounded-lg border-2 border-[#041C91]"
                  placeholder="+91 9876543210"
                />
              </div>

              {/* Address */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-[#041C91]">
                  Address
                </label>
                <Input
                  {...register('address')}
                  className="h-12 rounded-lg border-2 border-[#041C91]"
                  placeholder="Company address"
                />
              </div>

              {/* Submit */}
              <div className="pt-6">
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-12 bg-[#041C91] hover:bg-[#01033d] disabled:bg-gray-400 text-white rounded-lg"
                >
                  {isLoading ? 'Creating...' : 'Create'}
                </Button>
                {isError && (
                  <p className="text-red-500 text-sm mt-2">
                    Something went wrong while creating tenant.
                  </p>
                )}
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
