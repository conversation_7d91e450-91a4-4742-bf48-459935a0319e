import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';

// Fixed: Using type assertion to access api property from RTK Query

// Helper function to get cached query data from RTK Query
const selectCachedQueryData = (
  state: RootState,
  endpointName: string,
  args?: any
) => {
  const queryState = (state as any).api?.queries as
    | Record<string, { status?: string; data?: any }>
    | undefined;

  // Return null if api queries are not available
  if (!queryState) {
    return null;
  }

  // Find the query that matches our endpoint and args
  for (const [queryKey, queryData] of Object.entries(queryState)) {
    if (queryKey.includes(endpointName) && queryData?.status === 'fulfilled') {
      if (!args) {
        return queryData.data;
      }

      // For queries with args, we need to match the serialized args
      const serializedArgs = JSON.stringify(args);
      if (queryKey.includes(serializedArgs) || !args) {
        return queryData.data;
      }
    }
  }

  return null;
};

// Hook to get cached products data
export const useCachedProducts = () => {
  const cachedData = useSelector((state: RootState) =>
    selectCachedQueryData(state, 'getproducts')
  );

  return useMemo(() => {
    if (!cachedData) return [];
    return Array.isArray(cachedData) ? cachedData : [];
  }, [cachedData]);
};

// Hook to get cached product variants data
export const useCachedProductVariants = () => {
  const cachedData = useSelector((state: RootState) =>
    selectCachedQueryData(state, 'getProductVariants')
  );

  return useMemo(() => {
    if (!cachedData) return [];
    return Array.isArray(cachedData) ? cachedData : [];
  }, [cachedData]);
};

// Hook to get cached collections data
export const useCachedCollections = () => {
  const cachedData = useSelector((state: RootState) =>
    selectCachedQueryData(state, 'getCollections')
  );

  return useMemo(() => {
    if (!cachedData) return [];
    return Array.isArray(cachedData) ? cachedData : [];
  }, [cachedData]);
};

// Hook to get cached facets data
export const useCachedFacets = () => {
  const cachedData = useSelector((state: RootState) =>
    selectCachedQueryData(state, 'getFacets')
  );

  return useMemo(() => {
    if (!cachedData) return [];
    return Array.isArray(cachedData) ? cachedData : [];
  }, [cachedData]);
};

// Hook for searching products in cached data
export const useSearchCachedProducts = (searchTerm: string) => {
  const allProductVariants = useCachedProductVariants();

  return useMemo(() => {
    if (!searchTerm || searchTerm.length < 2) {
      return allProductVariants.slice(0, 20); // Return first 20 items when no search
    }

    const searchLower = searchTerm.toLowerCase();

    // Search in product variants
    const matchingVariants = allProductVariants.filter(
      (variant: any) =>
        variant.name?.toLowerCase().includes(searchLower) ||
        variant.sku?.toLowerCase().includes(searchLower)
    );

    return matchingVariants.slice(0, 20); // Limit to 20 results
  }, [allProductVariants, searchTerm]);
};

// Hook for searching collections in cached data
export const useSearchCachedCollections = (searchTerm: string) => {
  const allCollections = useCachedCollections();

  return useMemo(() => {
    if (!searchTerm || searchTerm.length < 2) {
      return allCollections.slice(0, 20); // Return first 20 items when no search
    }

    const searchLower = searchTerm.toLowerCase();

    const matchingCollections = allCollections.filter((collection: any) =>
      collection.name?.toLowerCase().includes(searchLower)
    );

    return matchingCollections.slice(0, 20); // Limit to 20 results
  }, [allCollections, searchTerm]);
};

// Hook for searching facets in cached data
export const useSearchCachedFacets = (searchTerm: string) => {
  const allFacets = useCachedFacets();

  return useMemo(() => {
    if (!searchTerm || searchTerm.length < 2) {
      // Return flattened facet values from all facets
      const flatFacetValues = allFacets.flatMap((facet: any) =>
        (facet.facetValues || []).map((value: any) => ({
          id: value.id,
          name: `${facet.name} ${value.name}`,
        }))
      );
      return flatFacetValues.slice(0, 20);
    }

    const searchLower = searchTerm.toLowerCase();

    // Search in facet names and return matching facet values
    const matchingFacets = allFacets.filter((facet: any) =>
      facet.name?.toLowerCase().includes(searchLower)
    );

    const flatFacetValues = matchingFacets.flatMap((facet: any) =>
      (facet.facetValues || []).map((value: any) => ({
        id: value.id,
        name: `${facet.name} ${value.name}`,
      }))
    );

    return flatFacetValues.slice(0, 20); // Limit to 20 results
  }, [allFacets, searchTerm]);
};

// Hook to get specific items by IDs from cached data
export const useCachedItemsByIds = (
  ids: string[],
  type: 'products' | 'collections' | 'productVariants' | 'facetValues'
) => {
  const allProducts = useCachedProducts();
  const allCollections = useCachedCollections();
  const allProductVariants = useCachedProductVariants();
  const allFacets = useCachedFacets();

  return useMemo(() => {
    if (!ids || ids.length === 0) return [];

    let sourceData: any[] = [];
    switch (type) {
      case 'products':
        sourceData = allProducts;
        break;
      case 'collections':
        sourceData = allCollections;
        break;
      case 'productVariants':
        sourceData = allProductVariants;
        break;
      case 'facetValues':
        // Flatten facet values from all facets
        sourceData = allFacets.flatMap((facet: any) =>
          (facet.facetValues || []).map((value: any) => ({
            id: value.id,
            name: `${facet.name} ${value.name}`,
            facetName: facet.name,
            valueName: value.name,
            ...value,
          }))
        );
        break;
    }

    return sourceData.filter((item: any) => ids.includes(item.id));
  }, [ids, type, allProducts, allCollections, allProductVariants, allFacets]);
};
