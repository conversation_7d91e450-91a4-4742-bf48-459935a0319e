import './global.css';
import '@b6ai/ui/styles/globals.css';
import { SimpleReduxProvider } from '../redux/providers/ReduxProvider';

export const metadata = {
  title: 'Welcome to enterprise-admin',
  description: 'Generated by create-nx-workspace',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <SimpleReduxProvider>{children}</SimpleReduxProvider>
      </body>
    </html>
  );
}
