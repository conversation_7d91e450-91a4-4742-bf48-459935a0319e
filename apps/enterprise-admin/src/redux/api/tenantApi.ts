import { apiSlice } from './apiSlice';
import { Tenant } from '../slices/tenantSlice';
import { PaginatedResponse, QueryParams } from '../types/api';

export interface CreateTenantRequest {
  name: string;
  domain: string;
  status: string;
  ownerId?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: string;
  settings?: Record<string, any>;
}

export interface OnboardTenantDto {
  name: string;
  domain: string;
  contactEmail: string;
  contactPhone?: string;
  address?: string;
  ownerId?: string;
  settings?: Record<string, any>;
}

export interface UpdateTenantRequest extends Partial<CreateTenantRequest> {
  id: string;
}

export interface TenantListParams extends QueryParams {
  status?: string;
  ownerId?: string;
  domain?: string;
}

export interface TenantSettings {
  theme?: 'light' | 'dark';
  timezone?: string;
  currency?: string;
  language?: string;
  features?: {
    analytics?: boolean;
    multiUser?: boolean;
    apiAccess?: boolean;
    customBranding?: boolean;
  };
  limits?: {
    users?: number;
    storage?: number;
    apiCalls?: number;
  };
  notifications?: {
    email?: boolean;
    sms?: boolean;
    push?: boolean;
  };
}

export interface TenantStats {
  totalUsers: number;
  activeUsers: number;
  storageUsed: number;
  apiCallsThisMonth: number;
  lastActivity: string;
}

export const tenantApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get paginated list of tenants
    getTenants: builder.query<PaginatedResponse<Tenant>, TenantListParams>({
      query: (params) => ({
        url: '/tenants',
        params,
      }),
      providesTags: ['Tenant'],
    }),

    // Get single tenant by ID
    getTenant: builder.query<Tenant, string>({
      query: (id) => `/tenants/${id}`,
      providesTags: (result, error, id) => [{ type: 'Tenant', id }],
    }),

    // Get current tenant (for the logged-in user's context)
    getCurrentTenant: builder.query<Tenant, void>({
      query: () => '/tenants/current',
      providesTags: ['Tenant'],
    }),

    // Onboard new tenant
    onboardTenant: builder.mutation<void, OnboardTenantDto>({
      query: (tenantData) => ({
        url: '/tenants/onboard',
        method: 'POST',
        body: tenantData,
      }),
      invalidatesTags: ['Tenant'],
    }),

    // Create new tenant
    createTenant: builder.mutation<Tenant, CreateTenantRequest>({
      query: (tenantData) => ({
        url: '/tenants',
        method: 'POST',
        body: tenantData,
      }),
      invalidatesTags: ['Tenant'],
    }),

    // Update tenant
    updateTenant: builder.mutation<Tenant, UpdateTenantRequest>({
      query: ({ id, ...patch }) => ({
        url: `/tenants/${id}`,
        method: 'PATCH',
        body: patch,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Tenant', id },
        'Tenant',
      ],
    }),

    // Soft delete tenant
    deleteTenant: builder.mutation<void, string>({
      query: (id) => ({
        url: `/tenants/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Tenant', id },
        'Tenant',
      ],
    }),

    // Restore soft deleted tenant
    restoreTenant: builder.mutation<Tenant, string>({
      query: (id) => ({
        url: `/tenants/${id}/restore`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Tenant', id },
        'Tenant',
      ],
    }),

    // Update tenant status
    updateTenantStatus: builder.mutation<
      Tenant,
      { id: string; status: string }
    >({
      query: ({ id, status }) => ({
        url: `/tenants/${id}/status`,
        method: 'PATCH',
        body: { status },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Tenant', id },
        'Tenant',
      ],
    }),

    // Update tenant settings
    updateTenantSettings: builder.mutation<
      Tenant,
      { id: string; settings: TenantSettings }
    >({
      query: ({ id, settings }) => ({
        url: `/tenants/${id}/settings`,
        method: 'PATCH',
        body: { settings },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Tenant', id },
        'Tenant',
      ],
    }),

    // Get tenant statistics
    getTenantStats: builder.query<TenantStats, string>({
      query: (id) => `/tenants/${id}/stats`,
      providesTags: (result, error, id) => [
        { type: 'Tenant', id: `${id}-stats` },
      ],
    }),

    // Check domain availability
    checkDomainAvailability: builder.query<{ available: boolean }, string>({
      query: (domain) => `/tenants/check-domain/${encodeURIComponent(domain)}`,
    }),

    // Get tenants by owner
    getTenantsByOwner: builder.query<Tenant[], string>({
      query: (ownerId) => `/tenants/by-owner/${ownerId}`,
      providesTags: ['Tenant'],
    }),

    // Bulk update tenants
    bulkUpdateTenants: builder.mutation<
      { updated: number; errors: any[] },
      { ids: string[]; updates: Partial<CreateTenantRequest> }
    >({
      query: ({ ids, updates }) => ({
        url: '/tenants/bulk-update',
        method: 'PATCH',
        body: { ids, updates },
      }),
      invalidatesTags: ['Tenant'],
    }),

    // Export tenants data
    exportTenants: builder.mutation<
      { downloadUrl: string },
      { format: 'csv' | 'xlsx'; filters?: TenantListParams }
    >({
      query: ({ format, filters }) => ({
        url: '/tenants/export',
        method: 'POST',
        body: { format, filters },
      }),
    }),
  }),
});

export const {
  useGetTenantsQuery,
  useGetTenantQuery,
  useGetCurrentTenantQuery,
  useOnboardTenantMutation,
  useCreateTenantMutation,
  useUpdateTenantMutation,
  useDeleteTenantMutation,
  useRestoreTenantMutation,
  useUpdateTenantStatusMutation,
  useUpdateTenantSettingsMutation,
  useGetTenantStatsQuery,
  useCheckDomainAvailabilityQuery,
  useGetTenantsByOwnerQuery,
  useBulkUpdateTenantsMutation,
  useExportTenantsMutation,
} = tenantApi;
