import { apiSlice } from './apiSlice';

export interface Company {
  id: string;
  name: string;
  email: string;
  phone?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  website?: string;
  logo?: string;
  industry?: string;
  size?: string;
  description?: string;
  settings?: {
    timezone: string;
    currency: string;
    language: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateCompanyRequest {
  name: string;
  email: string;
  phone?: string;
  address?: Company['address'];
  website?: string;
  industry?: string;
  size?: string;
  description?: string;
}

export interface UpdateCompanyRequest extends Partial<CreateCompanyRequest> {
  id: string;
}

export const companyApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getCompany: builder.query<Company, string>({
      query: (id) => `/companies/${id}`,
      providesTags: (result, error, id) => [{ type: 'Company', id }],
    }),
    getCurrentCompany: builder.query<Company, void>({
      query: () => '/companies/current',
      providesTags: ['Company'],
    }),
    createCompany: builder.mutation<Company, CreateCompanyRequest>({
      query: (companyData) => ({
        url: '/companies',
        method: 'POST',
        body: companyData,
      }),
      invalidatesTags: ['Company'],
    }),
    updateCompany: builder.mutation<Company, UpdateCompanyRequest>({
      query: ({ id, ...patch }) => ({
        url: `/companies/${id}`,
        method: 'PATCH',
        body: patch,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Company', id }],
    }),
    uploadCompanyLogo: builder.mutation<{ logoUrl: string }, { companyId: string; file: File }>({
      query: ({ companyId, file }) => {
        const formData = new FormData();
        formData.append('logo', file);
        return {
          url: `/companies/${companyId}/logo`,
          method: 'POST',
          body: formData,
        };
      },
      invalidatesTags: (result, error, { companyId }) => [{ type: 'Company', id: companyId }],
    }),
    deleteCompany: builder.mutation<void, string>({
      query: (id) => ({
        url: `/companies/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [{ type: 'Company', id }],
    }),
  }),
});

export const {
  useGetCompanyQuery,
  useGetCurrentCompanyQuery,
  useCreateCompanyMutation,
  useUpdateCompanyMutation,
  useUploadCompanyLogoMutation,
  useDeleteCompanyMutation,
} = companyApi;
