import { apiSlice } from './apiSlice';
import { OnboardTenantDto } from '../types/onboardTenant';

export const tenantOnboardApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Onboard new tenant - matches your controller exactly
    onboardTenant: builder.mutation<void, OnboardTenantDto>({
      query: (tenant) => ({
        url: '/tenants/onboard',
        method: 'POST',
        body: tenant,
      }),
      invalidatesTags: ['Tenant'],
    }),
  }),
});

export const { useOnboardTenantMutation } = tenantOnboardApi;
