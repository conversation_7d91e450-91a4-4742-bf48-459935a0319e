import { apiSlice } from './apiSlice';

export interface DashboardStats {
  totalUsers: number;
  totalOrders: number;
  totalRevenue: number;
  totalProducts: number;
  growthMetrics: {
    users: number;
    orders: number;
    revenue: number;
    products: number;
  };
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string;
    borderColor?: string;
  }[];
}

export interface AnalyticsParams {
  startDate?: string;
  endDate?: string;
  granularity?: 'day' | 'week' | 'month' | 'year';
}

export interface RevenueAnalytics {
  totalRevenue: number;
  revenueByPeriod: ChartData;
  revenueByProduct: ChartData;
  revenueByRegion: ChartData;
}

export interface UserAnalytics {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  userGrowth: ChartData;
  usersByRegion: ChartData;
}

export interface OrderAnalytics {
  totalOrders: number;
  completedOrders: number;
  pendingOrders: number;
  cancelledOrders: number;
  orderTrends: ChartData;
  averageOrderValue: number;
}

export const analyticsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getDashboardStats: builder.query<DashboardStats, AnalyticsParams>({
      query: (params) => ({
        url: '/analytics/dashboard',
        params,
      }),
      providesTags: ['Analytics'],
    }),
    getRevenueAnalytics: builder.query<RevenueAnalytics, AnalyticsParams>({
      query: (params) => ({
        url: '/analytics/revenue',
        params,
      }),
      providesTags: ['Analytics'],
    }),
    getUserAnalytics: builder.query<UserAnalytics, AnalyticsParams>({
      query: (params) => ({
        url: '/analytics/users',
        params,
      }),
      providesTags: ['Analytics'],
    }),
    getOrderAnalytics: builder.query<OrderAnalytics, AnalyticsParams>({
      query: (params) => ({
        url: '/analytics/orders',
        params,
      }),
      providesTags: ['Analytics'],
    }),
    exportAnalytics: builder.mutation<{ downloadUrl: string }, {
      type: 'revenue' | 'users' | 'orders';
      format: 'csv' | 'xlsx' | 'pdf';
      params: AnalyticsParams;
    }>({
      query: ({ type, format, params }) => ({
        url: `/analytics/${type}/export`,
        method: 'POST',
        body: { format, ...params },
      }),
    }),
  }),
});

export const {
  useGetDashboardStatsQuery,
  useGetRevenueAnalyticsQuery,
  useGetUserAnalyticsQuery,
  useGetOrderAnalyticsQuery,
  useExportAnalyticsMutation,
} = analyticsApi;
