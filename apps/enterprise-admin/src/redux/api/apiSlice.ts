import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type {
  BaseQueryFn,
  FetchArgs,
  FetchBaseQueryError,
} from '@reduxjs/toolkit/query';

// Base query with re-authentication logic
const baseQueryWithReauth: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const baseQuery = fetchBaseQuery({
    baseUrl: 'http://localhost:4001',
    prepareHeaders(headers, { getState }) {
      // const token = (getState() as RootState).auth.accessToken;
      // if (token) {
      //   headers.set('Authorization', `Bearer ${token}`);
      // }

      return headers;
    },
  });

  const result = await baseQuery(args, api, extraOptions);

  // if (result.error && result.error.status === 401) {
  //   // Try to get a new token
  //   const refreshResult = await baseQuery('/auth/refresh', api, extraOptions);

  //   if (refreshResult.data) {
  //     // Store the new token
  //     // api.dispatch(setCredentials(refreshResult.data));
  //     // Retry the original query
  //     result = await baseQuery(args, api, extraOptions);
  //   } else {
  //     // Refresh failed, logout user
  //     api.dispatch(logout());
  //   }
  // }

  return result;
};

// Create the API slice
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['User', 'Company', 'Product', 'Order', 'Analytics', 'Tenant'],
  endpoints: () => ({}),
});
