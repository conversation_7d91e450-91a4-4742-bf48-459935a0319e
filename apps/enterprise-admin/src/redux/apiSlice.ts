import type { BaseQueryFn, FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getBaseUrl } from './redux.helper';
import { ApiSliceIdentifier } from '../enums/api.enum';
import {
  AuthResData,
  setCredentials,
  unsetCredentials,
} from '../app/auth/authSlice';
import { getErrorMessage } from 'hooks/useApiErrorHandler';
// import { openSnackbar } from 'api/snackbar';
// import { SnackbarProps } from 'types/snackbar';

const authEndpoints = ['verifyOtp', 'login', 'sendOtp'];

/**
 * Base query function with re-Authentication handling and header preparation.
 * This function serves as an interceptor for API requests.
 *
 *
 * @param args - The fetch arguments for the request.
 * @param api - The API object provided by `createApi`.
 * @param extraOptions - Extra options for the query.
 */
const RESULT_ERROR_STATUS = 401;
const baseQueryWithReauth: BaseQueryFn<
  {
    url: string;
    method?: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    body?: any;
    apiSliceIdentifier?: ApiSliceIdentifier;
  },
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const state = api.getState() as any;

  const baseUrl = getBaseUrl(state, args.apiSliceIdentifier);
  const baseQuery = fetchBaseQuery({
    baseUrl,
    prepareHeaders(headers, { getState }) {
      const token = (getState() as any).auth.accessToken;
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      headers.set('x-origin', 'ecomdukes-admin');

      return headers;
    },
  });

  let result = await baseQuery(args, api, extraOptions);

  if (
    result.error?.status === RESULT_ERROR_STATUS &&
    !authEndpoints.includes(api.endpoint)
  ) {
    // try to get a new token
    const refreshResult = await baseQuery(
      {
        url: getBaseUrl(state) + '/auth/token-refresh',
        method: 'POST',
        body: { refreshToken: (api.getState() as any).auth.refreshToken },
      },
      api,
      extraOptions
    );
    if (refreshResult.data) {
      api.dispatch(setCredentials(refreshResult.data as AuthResData));
      result = await baseQuery(args, api, extraOptions);
    } else {
      await baseQuery(
        {
          url: getBaseUrl(state) + '/auth/logout',
          method: 'POST',
          body: {
            refreshToken: (api.getState() as any).auth.refreshToken,
          },
        },
        api,
        extraOptions
      );
      api.dispatch(unsetCredentials());
    }
  } else if (result?.error) {
    const errorMessage = getErrorMessage(result.error);
    // openSnackbar({
    //   message: errorMessage,
    //   open: true,
    //   variant: 'alert',
    //   alert: { color: 'error' },
    // } as SnackbarProps);
  } else {
    // do nothing
  }
  return result;
};

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  endpoints: () => ({}),
});
