import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface Tenant {
  id: string;
  name: string;
  domain: string;
  status: string;
  ownerId?: string;
  contactEmail?: string;
  contactPhone?: string;
  address?: string;
  settings?: Record<string, any>;
  createdAt?: string;
  updatedAt?: string;
  deletedAt?: string;
}

export interface TenantState {
  currentTenant: Tenant | null;
  tenants: Tenant[];
  selectedTenant: Tenant | null;
  isLoading: boolean;
  error: string | null;
  filters: {
    status?: string;
    search?: string;
    ownerId?: string;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

const initialState: TenantState = {
  currentTenant: null,
  tenants: [],
  selectedTenant: null,
  isLoading: false,
  error: null,
  filters: {},
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
};

const tenantSlice = createSlice({
  name: 'tenant',
  initialState,
  reducers: {
    setCurrentTenant: (state, action: PayloadAction<Tenant | null>) => {
      state.currentTenant = action.payload;
      state.error = null;
    },
    setTenants: (state, action: PayloadAction<Tenant[]>) => {
      state.tenants = action.payload;
      state.error = null;
    },
    setSelectedTenant: (state, action: PayloadAction<Tenant | null>) => {
      state.selectedTenant = action.payload;
    },
    addTenant: (state, action: PayloadAction<Tenant>) => {
      state.tenants.unshift(action.payload);
      state.pagination.total += 1;
    },
    updateTenant: (state, action: PayloadAction<Tenant>) => {
      const index = state.tenants.findIndex(t => t.id === action.payload.id);
      if (index !== -1) {
        state.tenants[index] = action.payload;
      }
      if (state.currentTenant?.id === action.payload.id) {
        state.currentTenant = action.payload;
      }
      if (state.selectedTenant?.id === action.payload.id) {
        state.selectedTenant = action.payload;
      }
    },
    removeTenant: (state, action: PayloadAction<string>) => {
      state.tenants = state.tenants.filter(t => t.id !== action.payload);
      state.pagination.total -= 1;
      if (state.selectedTenant?.id === action.payload) {
        state.selectedTenant = null;
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
    setFilters: (state, action: PayloadAction<Partial<TenantState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    setPagination: (state, action: PayloadAction<Partial<TenantState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
    resetTenantState: (state) => {
      return initialState;
    },
  },
});

export const {
  setCurrentTenant,
  setTenants,
  setSelectedTenant,
  addTenant,
  updateTenant,
  removeTenant,
  setLoading,
  setError,
  setFilters,
  clearFilters,
  setPagination,
  clearError,
  resetTenantState,
} = tenantSlice.actions;

export default tenantSlice.reducer;

// Selectors
export const selectCurrentTenant = (state: { tenant: TenantState }) => state.tenant.currentTenant;
export const selectTenants = (state: { tenant: TenantState }) => state.tenant.tenants;
export const selectSelectedTenant = (state: { tenant: TenantState }) => state.tenant.selectedTenant;
export const selectTenantLoading = (state: { tenant: TenantState }) => state.tenant.isLoading;
export const selectTenantError = (state: { tenant: TenantState }) => state.tenant.error;
export const selectTenantFilters = (state: { tenant: TenantState }) => state.tenant.filters;
export const selectTenantPagination = (state: { tenant: TenantState }) => state.tenant.pagination;

// Derived selectors
export const selectActiveTenants = (state: { tenant: TenantState }) =>
  state.tenant.tenants.filter(tenant => tenant.status === 'active');

export const selectTenantsByStatus = (status: string) => (state: { tenant: TenantState }) =>
  state.tenant.tenants.filter(tenant => tenant.status === status);

export const selectTenantById = (id: string) => (state: { tenant: TenantState }) =>
  state.tenant.tenants.find(tenant => tenant.id === id);
