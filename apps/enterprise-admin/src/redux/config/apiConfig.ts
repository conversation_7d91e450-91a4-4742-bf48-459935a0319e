// API Configuration
export const API_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api',
  timeout: 30000, // 30 seconds
  retryAttempts: 3,
  retryDelay: 1000, // 1 second
};

// API Endpoints
export const API_ENDPOINTS = {
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    logout: '/auth/logout',
    refresh: '/auth/refresh',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password',
    verifyEmail: '/auth/verify-email',
    me: '/auth/me',
  },
  users: {
    list: '/users',
    create: '/users',
    get: (id: string) => `/users/${id}`,
    update: (id: string) => `/users/${id}`,
    delete: (id: string) => `/users/${id}`,
    invite: '/users/invite',
    resendInvitation: (id: string) => `/users/${id}/resend-invitation`,
  },
  companies: {
    current: '/companies/current',
    get: (id: string) => `/companies/${id}`,
    create: '/companies',
    update: (id: string) => `/companies/${id}`,
    delete: (id: string) => `/companies/${id}`,
    uploadLogo: (id: string) => `/companies/${id}/logo`,
  },
  analytics: {
    dashboard: '/analytics/dashboard',
    revenue: '/analytics/revenue',
    users: '/analytics/users',
    orders: '/analytics/orders',
    export: (type: string) => `/analytics/${type}/export`,
  },
};

// Cache configuration
export const CACHE_CONFIG = {
  // Cache times in seconds
  defaultCacheTime: 5 * 60, // 5 minutes
  userCacheTime: 10 * 60, // 10 minutes
  companyCacheTime: 30 * 60, // 30 minutes
  analyticsCacheTime: 2 * 60, // 2 minutes
  
  // Tag types for cache invalidation
  tagTypes: ['User', 'Company', 'Product', 'Order', 'Analytics'] as const,
};

// Request configuration
export const REQUEST_CONFIG = {
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  credentials: 'include' as RequestCredentials,
};

// Error messages
export const ERROR_MESSAGES = {
  network: 'Network error. Please check your connection.',
  unauthorized: 'You are not authorized to perform this action.',
  forbidden: 'Access denied.',
  notFound: 'The requested resource was not found.',
  serverError: 'Internal server error. Please try again later.',
  timeout: 'Request timeout. Please try again.',
  unknown: 'An unexpected error occurred.',
};

// Success messages
export const SUCCESS_MESSAGES = {
  login: 'Successfully logged in!',
  logout: 'Successfully logged out!',
  register: 'Account created successfully!',
  passwordReset: 'Password reset successfully!',
  emailVerified: 'Email verified successfully!',
  profileUpdated: 'Profile updated successfully!',
  companyUpdated: 'Company information updated successfully!',
  userCreated: 'User created successfully!',
  userUpdated: 'User updated successfully!',
  userDeleted: 'User deleted successfully!',
  invitationSent: 'Invitation sent successfully!',
};
