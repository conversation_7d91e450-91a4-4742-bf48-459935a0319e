import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '../hooks';
import {
  selectCurrentTenant,
  selectTenants,
  selectSelectedTenant,
  selectTenantLoading,
  selectTenantError,
  selectTenantFilters,
  selectTenantPagination,
  selectActiveTenants,
  selectTenantsByStatus,
  selectTenantById,
  setCurrentTenant,
  setSelectedTenant,
  setFilters,
  clearFilters,
  setPagination,
  clearError,
} from '../slices/tenantSlice';
import {
  useGetCurrentTenantQuery,
  useGetTenantsQuery,
  useCreateTenantMutation,
  useUpdateTenantMutation,
  useDeleteTenantMutation,
  useUpdateTenantStatusMutation,
  useUpdateTenantSettingsMutation,
  useGetTenantStatsQuery,
  useCheckDomainAvailabilityQuery,
} from '../api/tenantApi';

export const useTenant = () => {
  const dispatch = useAppDispatch();

  // Selectors
  const currentTenant = useAppSelector(selectCurrentTenant);
  const tenants = useAppSelector(selectTenants);
  const selectedTenant = useAppSelector(selectSelectedTenant);
  const isLoading = useAppSelector(selectTenantLoading);
  const error = useAppSelector(selectTenantError);
  const filters = useAppSelector(selectTenantFilters);
  const pagination = useAppSelector(selectTenantPagination);
  const activeTenants = useAppSelector(selectActiveTenants);

  // API hooks
  const { data: currentTenantData, isLoading: currentTenantLoading } = useGetCurrentTenantQuery();
  const [createTenant, { isLoading: isCreating }] = useCreateTenantMutation();
  const [updateTenant, { isLoading: isUpdating }] = useUpdateTenantMutation();
  const [deleteTenant, { isLoading: isDeleting }] = useDeleteTenantMutation();
  const [updateTenantStatus, { isLoading: isUpdatingStatus }] = useUpdateTenantStatusMutation();
  const [updateTenantSettings, { isLoading: isUpdatingSettings }] = useUpdateTenantSettingsMutation();

  // Actions
  const switchTenant = useCallback((tenant: any) => {
    dispatch(setCurrentTenant(tenant));
  }, [dispatch]);

  const selectTenant = useCallback((tenant: any) => {
    dispatch(setSelectedTenant(tenant));
  }, [dispatch]);

  const updateFilters = useCallback((newFilters: any) => {
    dispatch(setFilters(newFilters));
  }, [dispatch]);

  const resetFilters = useCallback(() => {
    dispatch(clearFilters());
  }, [dispatch]);

  const updatePagination = useCallback((newPagination: any) => {
    dispatch(setPagination(newPagination));
  }, [dispatch]);

  const clearTenantError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Utility functions
  const getTenantById = useCallback((id: string) => {
    return selectTenantById(id)({ tenant: { tenants, currentTenant, selectedTenant, isLoading, error, filters, pagination } });
  }, [tenants, currentTenant, selectedTenant, isLoading, error, filters, pagination]);

  const getTenantsByStatus = useCallback((status: string) => {
    return selectTenantsByStatus(status)({ tenant: { tenants, currentTenant, selectedTenant, isLoading, error, filters, pagination } });
  }, [tenants, currentTenant, selectedTenant, isLoading, error, filters, pagination]);

  const isCurrentTenant = useCallback((tenantId: string) => {
    return currentTenant?.id === tenantId;
  }, [currentTenant]);

  const canManageTenant = useCallback((tenantId: string) => {
    // Add your permission logic here
    // For example, check if user is owner or has admin role
    return true; // Placeholder
  }, []);

  // Create tenant with error handling
  const handleCreateTenant = useCallback(async (tenantData: any) => {
    try {
      const result = await createTenant(tenantData).unwrap();
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error };
    }
  }, [createTenant]);

  // Update tenant with error handling
  const handleUpdateTenant = useCallback(async (id: string, updates: any) => {
    try {
      const result = await updateTenant({ id, ...updates }).unwrap();
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error };
    }
  }, [updateTenant]);

  // Delete tenant with error handling
  const handleDeleteTenant = useCallback(async (id: string) => {
    try {
      await deleteTenant(id).unwrap();
      return { success: true };
    } catch (error) {
      return { success: false, error };
    }
  }, [deleteTenant]);

  // Update tenant status with error handling
  const handleUpdateTenantStatus = useCallback(async (id: string, status: string) => {
    try {
      const result = await updateTenantStatus({ id, status }).unwrap();
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error };
    }
  }, [updateTenantStatus]);

  // Update tenant settings with error handling
  const handleUpdateTenantSettings = useCallback(async (id: string, settings: any) => {
    try {
      const result = await updateTenantSettings({ id, settings }).unwrap();
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error };
    }
  }, [updateTenantSettings]);

  return {
    // State
    currentTenant,
    tenants,
    selectedTenant,
    isLoading,
    error,
    filters,
    pagination,
    activeTenants,

    // Loading states
    currentTenantLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isUpdatingStatus,
    isUpdatingSettings,

    // Actions
    switchTenant,
    selectTenant,
    updateFilters,
    resetFilters,
    updatePagination,
    clearTenantError,

    // Utilities
    getTenantById,
    getTenantsByStatus,
    isCurrentTenant,
    canManageTenant,

    // API operations
    handleCreateTenant,
    handleUpdateTenant,
    handleDeleteTenant,
    handleUpdateTenantStatus,
    handleUpdateTenantSettings,

    // Raw API hooks (for advanced usage)
    createTenant,
    updateTenant,
    deleteTenant,
    updateTenantStatus,
    updateTenantSettings,
  };
};

// Hook for tenant statistics
export const useTenantStats = (tenantId?: string) => {
  const { data: stats, isLoading, error } = useGetTenantStatsQuery(tenantId!, {
    skip: !tenantId,
  });

  return {
    stats,
    isLoading,
    error,
  };
};

// Hook for domain availability checking
export const useDomainAvailability = () => {
  const [checkDomain, { data, isLoading, error }] = useCheckDomainAvailabilityQuery();

  const checkAvailability = useCallback(async (domain: string) => {
    try {
      const result = await checkDomain(domain).unwrap();
      return result;
    } catch (error) {
      return { available: false, error };
    }
  }, [checkDomain]);

  return {
    checkAvailability,
    isLoading,
    error,
    lastResult: data,
  };
};
