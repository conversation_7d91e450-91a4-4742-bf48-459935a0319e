# Redux Setup for Enterprise Admin

This directory contains the Redux store configuration using Redux Toolkit (RTK) and RTK Query for API integration.

## Structure

```
redux/
├── api/                    # RTK Query API slices
│   ├── apiSlice.ts        # Base API slice with auth
│   ├── authApi.ts         # Authentication endpoints
│   ├── companyApi.ts      # Company management endpoints
│   ├── userApi.ts         # User management endpoints
│   └── analyticsApi.ts    # Analytics endpoints
├── slices/                # Redux slices
│   ├── authSlice.ts       # Authentication state
│   └── uiSlice.ts         # UI state (sidebar, theme, etc.)
├── middleware/            # Custom middleware
│   └── authMiddleware.ts  # Auth-related side effects
├── providers/             # React providers
│   └── ReduxProvider.tsx  # Redux provider component
├── types/                 # TypeScript types
│   └── api.ts            # Common API types
├── config/               # Configuration
│   └── apiConfig.ts      # API endpoints and config
├── hooks.ts              # Typed Redux hooks
├── store.ts              # Store configuration
└── index.ts              # Main exports
```

## Usage

### 1. Setup Provider

Wrap your app with the Redux provider:

```tsx
import { ReduxProvider } from '@/redux';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <ReduxProvider>
          {children}
        </ReduxProvider>
      </body>
    </html>
  );
}
```

### 2. Using Hooks

Use the typed hooks instead of the default ones:

```tsx
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { selectCurrentUser } from '@/redux/slices/authSlice';

function UserProfile() {
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectCurrentUser);
  
  // Component logic
}
```

### 3. API Queries

Use RTK Query hooks for API calls:

```tsx
import { useGetCurrentUserQuery, useLoginMutation } from '@/redux/api/authApi';

function LoginForm() {
  const [login, { isLoading, error }] = useLoginMutation();
  const { data: user, isLoading: userLoading } = useGetCurrentUserQuery();
  
  const handleLogin = async (credentials) => {
    try {
      await login(credentials).unwrap();
    } catch (error) {
      console.error('Login failed:', error);
    }
  };
}
```

### 4. State Management

Dispatch actions to update state:

```tsx
import { useAppDispatch } from '@/redux/hooks';
import { setTheme, addNotification } from '@/redux/slices/uiSlice';

function ThemeToggle() {
  const dispatch = useAppDispatch();
  
  const toggleTheme = () => {
    dispatch(setTheme('dark'));
    dispatch(addNotification({
      type: 'success',
      title: 'Theme Changed',
      message: 'Dark theme enabled',
    }));
  };
}
```

## Features

### Authentication
- Login/logout with JWT tokens
- Automatic token refresh
- Persistent auth state
- Auto-logout on 401 errors

### API Integration
- Centralized API configuration
- Automatic caching and invalidation
- Error handling
- Loading states
- Optimistic updates

### UI State Management
- Sidebar state
- Theme management
- Notifications
- Loading states
- Modal management

### Type Safety
- Fully typed with TypeScript
- Type-safe API responses
- Typed Redux hooks
- IntelliSense support

## Environment Variables

Add these to your `.env.local`:

```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api
```

## Extending

### Adding New API Endpoints

1. Create a new API slice in `api/` directory
2. Inject endpoints using `apiSlice.injectEndpoints()`
3. Export the generated hooks
4. Add to main exports in `index.ts`

### Adding New State

1. Create a new slice in `slices/` directory
2. Add to store configuration in `store.ts`
3. Export selectors and actions
4. Add to main exports in `index.ts`

## Best Practices

1. Use RTK Query for all API calls
2. Keep slices focused and small
3. Use selectors for derived state
4. Leverage automatic caching
5. Handle loading and error states
6. Use TypeScript for type safety
