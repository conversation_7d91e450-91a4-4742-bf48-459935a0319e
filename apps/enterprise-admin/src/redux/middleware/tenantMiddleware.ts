import { createListenerMiddleware, isAnyOf } from '@reduxjs/toolkit';
import { setCurrentTenant, resetTenantState } from '../slices/tenantSlice';
import { logout } from '../slices/authSlice';
import { addNotification } from '../slices/uiSlice';
import { tenantApi } from '../api/tenantApi';

// Create the middleware instance
export const tenantMiddleware = createListenerMiddleware();

// Listen for current tenant changes
tenantMiddleware.startListening({
  actionCreator: setCurrentTenant,
  effect: async (action, listenerApi) => {
    const tenant = action.payload;

    if (tenant) {
      // Store current tenant in localStorage
      localStorage.setItem('currentTenant', JSON.stringify(tenant));

      // Show notification for tenant switch
      listenerApi.dispatch(
        addNotification({
          type: 'info',
          title: 'Tenant Switched',
          message: `Now managing ${tenant.name}`,
        })
      );

      // Apply tenant-specific settings if available
      if (tenant.settings) {
        // You can dispatch theme changes, feature flags, etc. based on tenant settings
        const settings = tenant.settings as any;

        // Example: Apply theme from tenant settings
        if (settings.theme) {
          // Dispatch theme change action if needed
          // listenerApi.dispatch(setTheme(settings.theme));
        }
      }
    } else {
      // Clear tenant data from localStorage
      localStorage.removeItem('currentTenant');
    }
  },
});

// Listen for logout and clear tenant state
tenantMiddleware.startListening({
  actionCreator: logout,
  effect: async (action, listenerApi) => {
    // Clear tenant state on logout
    listenerApi.dispatch(resetTenantState());
    localStorage.removeItem('currentTenant');
  },
});

// Listen for tenant onboard success
tenantMiddleware.startListening({
  matcher: tenantApi.endpoints.onboardTenant.matchFulfilled,
  effect: async (action, listenerApi) => {
    listenerApi.dispatch(
      addNotification({
        type: 'success',
        title: 'Tenant Onboarded',
        message: 'Tenant has been successfully onboarded',
      })
    );
  },
});

// Listen for tenant creation success
tenantMiddleware.startListening({
  matcher: tenantApi.endpoints.createTenant.matchFulfilled,
  effect: async (action, listenerApi) => {
    const newTenant = action.payload;

    listenerApi.dispatch(
      addNotification({
        type: 'success',
        title: 'Tenant Created',
        message: `${newTenant.name} has been created successfully`,
      })
    );
  },
});

// Listen for tenant update success
tenantMiddleware.startListening({
  matcher: tenantApi.endpoints.updateTenant.matchFulfilled,
  effect: async (action, listenerApi) => {
    const updatedTenant = action.payload;

    listenerApi.dispatch(
      addNotification({
        type: 'success',
        title: 'Tenant Updated',
        message: `${updatedTenant.name} has been updated successfully`,
      })
    );
  },
});

// Listen for tenant deletion success
tenantMiddleware.startListening({
  matcher: tenantApi.endpoints.deleteTenant.matchFulfilled,
  effect: async (action, listenerApi) => {
    listenerApi.dispatch(
      addNotification({
        type: 'success',
        title: 'Tenant Deleted',
        message: 'Tenant has been deleted successfully',
      })
    );
  },
});

// Listen for tenant status changes
tenantMiddleware.startListening({
  matcher: tenantApi.endpoints.updateTenantStatus.matchFulfilled,
  effect: async (action, listenerApi) => {
    const updatedTenant = action.payload;

    listenerApi.dispatch(
      addNotification({
        type: 'info',
        title: 'Status Updated',
        message: `${updatedTenant.name} status changed to ${updatedTenant.status}`,
      })
    );
  },
});

// Listen for API errors related to tenants
tenantMiddleware.startListening({
  predicate: (action) => {
    return (
      action.type.includes('tenantApi') && action.type.endsWith('/rejected')
    );
  },
  effect: async (action, listenerApi) => {
    const error = action.payload as any;

    // Handle specific tenant-related errors
    if (error?.status === 403) {
      listenerApi.dispatch(
        addNotification({
          type: 'error',
          title: 'Access Denied',
          message: 'You do not have permission to access this tenant',
        })
      );
    } else if (error?.status === 404) {
      listenerApi.dispatch(
        addNotification({
          type: 'error',
          title: 'Tenant Not Found',
          message: 'The requested tenant could not be found',
        })
      );
    } else if (error?.data?.message) {
      listenerApi.dispatch(
        addNotification({
          type: 'error',
          title: 'Tenant Operation Failed',
          message: error.data.message,
        })
      );
    }
  },
});

// Listen for domain availability checks
tenantMiddleware.startListening({
  matcher: tenantApi.endpoints.checkDomainAvailability.matchFulfilled,
  effect: async (action, listenerApi) => {
    const { available } = action.payload;
    const domain = action.meta.arg.originalArgs;

    if (!available) {
      listenerApi.dispatch(
        addNotification({
          type: 'warning',
          title: 'Domain Unavailable',
          message: `The domain "${domain}" is already taken`,
        })
      );
    }
  },
});
