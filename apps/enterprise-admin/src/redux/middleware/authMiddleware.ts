import { createListenerMiddleware, isAnyOf } from '@reduxjs/toolkit';
import { logout, setCredentials } from '../slices/authSlice';
import { apiSlice } from '../api/apiSlice';

// Create the middleware instance
export const authMiddleware = createListenerMiddleware();

// Listen for login success
authMiddleware.startListening({
  matcher: isAnyOf(setCredentials),
  effect: async (action, listenerApi) => {
    // Store auth data in localStorage
    const { user, token, refreshToken } = action.payload;
    localStorage.setItem('token', token);
    if (refreshToken) {
      localStorage.setItem('refreshToken', refreshToken);
    }
    localStorage.setItem('user', JSON.stringify(user));
  },
});

// Listen for logout
authMiddleware.startListening({
  actionCreator: logout,
  effect: async (action, listenerApi) => {
    // Clear auth data from localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    
    // Clear all API cache
    listenerApi.dispatch(apiSlice.util.resetApiState());
  },
});

// Listen for 401 errors and auto-logout
authMiddleware.startListening({
  predicate: (action) => {
    return (
      action.type.endsWith('/rejected') &&
      action.payload?.status === 401
    );
  },
  effect: async (action, listenerApi) => {
    // Auto logout on 401 errors
    listenerApi.dispatch(logout());
  },
});
